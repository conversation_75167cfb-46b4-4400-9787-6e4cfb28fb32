"""
CCR Score API Routes for MSME-facing application.

Provides endpoints for:
- CCR score calculation and retrieval
- Score breakdown and trends
- PDF/QR export functionality
- Score improvement recommendations

Author: Credit Chakra Team
Version: 1.0.0
"""
from fastapi import APIRouter, HTTPException, status, Depends, Query
from fastapi.responses import FileResponse
from typing import List, Optional
from datetime import datetime, timedelta
import uuid
import logging

from models.msme import CCRScoreBreakdown, CCRTrend, MSMEProfile
from services.ccr_scoring import ccr_scoring_service
from firebase.init import get_firestore_client
from utils.error_handler import handle_common_exceptions, MSMENotFoundError

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/{msme_id}/ccr-score", response_model=dict)
@handle_common_exceptions
async def get_ccr_score(msme_id: str):
    """
    Get current CCR score with detailed breakdown for an MSME.
    
    Returns:
        - Overall CCR score (0-100)
        - Breakdown by 5 components
        - Last update timestamp
        - Risk band based on score
    """
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_doc = db.collection('msmes').document(msme_id).get()
        if not msme_doc.exists:
            raise MSMENotFoundError(f"MSME with ID {msme_id} not found")
        
        msme_data = msme_doc.to_dict()
        
        # Calculate current CCR score
        overall_score, breakdown = await ccr_scoring_service.calculate_ccr_score(msme_id)
        
        # Determine risk band based on CCR score
        if overall_score >= 70:
            risk_band = "green"
            risk_label = "Low"
        elif overall_score >= 40:
            risk_band = "yellow"
            risk_label = "Medium"
        else:
            risk_band = "red"
            risk_label = "High"
        
        # Update MSME document with latest CCR score
        update_data = {
            'ccr_score': overall_score,
            'ccr_breakdown': breakdown.dict(),
            'last_ccr_update': datetime.now(),
            'risk_band': risk_band
        }
        
        db.collection('msmes').document(msme_id).update(update_data)
        
        return {
            "msme_id": msme_id,
            "msme_name": msme_data.get('name'),
            "ccr_score": round(overall_score, 1),
            "risk_band": risk_band,
            "risk_label": risk_label,
            "score_breakdown": {
                "gst_compliance": round(breakdown.gst_compliance, 1),
                "upi_diversity": round(breakdown.upi_diversity, 1),
                "digital_presence": round(breakdown.digital_presence, 1),
                "financial_health": round(breakdown.financial_health, 1),
                "business_stability": round(breakdown.business_stability, 1)
            },
            "component_descriptions": {
                "gst_compliance": "GST filing regularity and compliance",
                "upi_diversity": "UPI transaction diversity and volume",
                "digital_presence": "Online presence and customer reviews",
                "financial_health": "Financial stability and cash flow",
                "business_stability": "Business longevity and growth trends"
            },
            "last_updated": datetime.now().isoformat(),
            "score_scale": "0-100 (Higher is better)"
        }
        
    except MSMENotFoundError:
        raise
    except Exception as e:
        logger.error(f"Error getting CCR score for MSME {msme_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate CCR score: {str(e)}"
        )

@router.get("/{msme_id}/ccr-trends", response_model=dict)
@handle_common_exceptions
async def get_ccr_trends(
    msme_id: str,
    months: int = Query(default=6, ge=1, le=24, description="Number of months of trend data")
):
    """
    Get CCR score trends over time.
    
    Args:
        msme_id: MSME identifier
        months: Number of months of historical data (1-24)
    
    Returns:
        Historical CCR scores and component breakdowns
    """
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_doc = db.collection('msmes').document(msme_id).get()
        if not msme_doc.exists:
            raise MSMENotFoundError(f"MSME with ID {msme_id} not found")
        
        # Get historical CCR data (this would be stored separately in production)
        # For now, we'll generate sample trend data
        trends = []
        current_date = datetime.now()
        
        for i in range(months):
            trend_date = current_date - timedelta(days=i * 30)
            
            # In production, this would fetch actual historical data
            # For demo, we'll calculate current score and add some variation
            overall_score, breakdown = await ccr_scoring_service.calculate_ccr_score(msme_id)
            
            # Add some realistic variation for historical data
            variation = (i * 2) if i < 3 else (6 - i)  # Simulate improvement over time
            historical_score = max(0, min(100, overall_score - variation))
            
            trends.append({
                "date": trend_date.isoformat(),
                "ccr_score": round(historical_score, 1),
                "gst_compliance": round(max(0, breakdown.gst_compliance - variation), 1),
                "upi_diversity": round(max(0, breakdown.upi_diversity - variation), 1),
                "digital_presence": round(max(0, breakdown.digital_presence - variation), 1),
                "financial_health": round(max(0, breakdown.financial_health - variation), 1),
                "business_stability": round(max(0, breakdown.business_stability - variation), 1)
            })
        
        # Reverse to show oldest first
        trends.reverse()
        
        # Calculate trend direction
        if len(trends) >= 2:
            recent_score = trends[-1]["ccr_score"]
            older_score = trends[-2]["ccr_score"]
            trend_direction = "improving" if recent_score > older_score else "declining" if recent_score < older_score else "stable"
        else:
            trend_direction = "stable"
        
        return {
            "msme_id": msme_id,
            "trend_period_months": months,
            "trend_direction": trend_direction,
            "current_score": trends[-1]["ccr_score"] if trends else 0,
            "score_change": round(trends[-1]["ccr_score"] - trends[0]["ccr_score"], 1) if len(trends) >= 2 else 0,
            "trends": trends
        }
        
    except MSMENotFoundError:
        raise
    except Exception as e:
        logger.error(f"Error getting CCR trends for MSME {msme_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get CCR trends: {str(e)}"
        )

@router.get("/{msme_id}/score-recommendations", response_model=dict)
@handle_common_exceptions
async def get_score_recommendations(msme_id: str):
    """
    Get personalized recommendations to improve CCR score.
    
    Returns:
        - Component-specific improvement suggestions
        - Priority actions
        - Potential score impact
    """
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_doc = db.collection('msmes').document(msme_id).get()
        if not msme_doc.exists:
            raise MSMENotFoundError(f"MSME with ID {msme_id} not found")
        
        # Get current score breakdown
        overall_score, breakdown = await ccr_scoring_service.calculate_ccr_score(msme_id)
        
        recommendations = []
        
        # Generate recommendations based on component scores
        if breakdown.gst_compliance < 70:
            recommendations.append({
                "component": "gst_compliance",
                "current_score": round(breakdown.gst_compliance, 1),
                "priority": "high" if breakdown.gst_compliance < 40 else "medium",
                "title": "Improve GST Compliance",
                "description": "Regular GST filing and compliance can significantly boost your score",
                "actions": [
                    "File GST returns on time every month",
                    "Maintain accurate GST records",
                    "Resolve any pending GST issues"
                ],
                "potential_impact": "+15-25 points",
                "estimated_timeframe": "2-3 months"
            })
        
        if breakdown.upi_diversity < 60:
            recommendations.append({
                "component": "upi_diversity",
                "current_score": round(breakdown.upi_diversity, 1),
                "priority": "medium",
                "title": "Increase Digital Payment Usage",
                "description": "More diverse UPI transactions show active business operations",
                "actions": [
                    "Accept UPI payments from more customers",
                    "Use UPI for business purchases",
                    "Increase transaction frequency"
                ],
                "potential_impact": "+10-20 points",
                "estimated_timeframe": "1-2 months"
            })
        
        if breakdown.digital_presence < 50:
            recommendations.append({
                "component": "digital_presence",
                "current_score": round(breakdown.digital_presence, 1),
                "priority": "medium",
                "title": "Enhance Online Presence",
                "description": "Strong digital presence builds customer trust and credibility",
                "actions": [
                    "Encourage customers to leave Google reviews",
                    "Maintain active social media presence",
                    "Update business listings on online platforms"
                ],
                "potential_impact": "+8-15 points",
                "estimated_timeframe": "2-4 months"
            })
        
        if breakdown.financial_health < 60:
            recommendations.append({
                "component": "financial_health",
                "current_score": round(breakdown.financial_health, 1),
                "priority": "high",
                "title": "Strengthen Financial Position",
                "description": "Stable revenue and growth improve creditworthiness",
                "actions": [
                    "Focus on revenue growth and stability",
                    "Maintain consistent business operations",
                    "Diversify income sources"
                ],
                "potential_impact": "+12-22 points",
                "estimated_timeframe": "3-6 months"
            })
        
        if breakdown.business_stability < 55:
            recommendations.append({
                "component": "business_stability",
                "current_score": round(breakdown.business_stability, 1),
                "priority": "low",
                "title": "Build Business Stability",
                "description": "Consistent data and long-term operations show reliability",
                "actions": [
                    "Maintain regular business operations",
                    "Keep consistent records across platforms",
                    "Build long-term customer relationships"
                ],
                "potential_impact": "+5-12 points",
                "estimated_timeframe": "6-12 months"
            })
        
        # Sort by priority
        priority_order = {"high": 1, "medium": 2, "low": 3}
        recommendations.sort(key=lambda x: priority_order.get(x["priority"], 4))
        
        return {
            "msme_id": msme_id,
            "current_ccr_score": round(overall_score, 1),
            "total_recommendations": len(recommendations),
            "max_potential_improvement": sum(int(r["potential_impact"].split("-")[1].split(" ")[0]) for r in recommendations),
            "recommendations": recommendations,
            "next_review_date": (datetime.now() + timedelta(days=30)).isoformat()
        }
        
    except MSMENotFoundError:
        raise
    except Exception as e:
        logger.error(f"Error getting recommendations for MSME {msme_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendations: {str(e)}"
        )

@router.post("/{msme_id}/export-score", response_model=dict)
@handle_common_exceptions
async def export_ccr_score(
    msme_id: str,
    format: str = Query(default="pdf", regex="^(pdf|qr)$", description="Export format: pdf or qr")
):
    """
    Export CCR score as PDF report or QR code.
    
    Args:
        msme_id: MSME identifier
        format: Export format (pdf or qr)
    
    Returns:
        Download URL for the exported file
    """
    try:
        # This would integrate with a PDF generation service
        # For now, return a placeholder response
        
        export_id = str(uuid.uuid4())
        
        if format == "pdf":
            filename = f"ccr_score_report_{msme_id}_{export_id}.pdf"
            file_type = "PDF Report"
        else:  # qr
            filename = f"ccr_score_qr_{msme_id}_{export_id}.png"
            file_type = "QR Code"
        
        # In production, this would:
        # 1. Generate the actual PDF/QR file
        # 2. Store it in cloud storage
        # 3. Return the download URL
        
        return {
            "export_id": export_id,
            "msme_id": msme_id,
            "format": format,
            "file_type": file_type,
            "filename": filename,
            "download_url": f"/api/ccr/{msme_id}/download/{export_id}",
            "expires_at": (datetime.now() + timedelta(hours=24)).isoformat(),
            "status": "ready"
        }
        
    except Exception as e:
        logger.error(f"Error exporting CCR score for MSME {msme_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export CCR score: {str(e)}"
        )
